﻿# apps/monitoring/overlays/prod/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# 生产环境的监控配置
# 直接定义 Helm Chart，包含基础配置和生产环境覆盖
helmCharts:
- name: kube-prometheus-stack
  repo: https://prometheus-community.github.io/helm-charts
  version: 75.6.0
  releaseName: monitoring-stack
  namespace: monitoring
  valuesFile: values-prod.yaml            # 包含所有配置的 values 文件
  includeCRDs: true                       # 确保安装 CRD
