# 哪吒监控面板 GitOps 部署配置

## 概述

本配置提供了通过 ArgoCD 在 K3s 集群中部署哪吒监控面板的完整 GitOps 解决方案。

## 功能特性

- ✅ 完全的 GitOps 自动化部署
- ✅ 通过 `nezha.liuovo.com` 域名访问
- ✅ 自动 HTTPS/TLS 证书管理 (Let's Encrypt)
- ✅ Traefik Ingress 路由配置
- ✅ 生产环境优化配置
- ✅ 健康检查和资源限制
- ✅ 自动同步和自愈能力

## 目录结构

```
apps/nezha/
├── base/                           # 基础 Kubernetes 资源
│   ├── namespace.yaml             # 命名空间定义
│   ├── configmap.yaml             # 基础配置文件
│   ├── deployment.yaml            # 部署配置
│   ├── service.yaml               # 服务配置
│   └── kustomization.yaml         # 基础 Kustomization
└── overlays/
    └── prod/                      # 生产环境覆盖配置
        ├── ingress.yaml           # Ingress 路由配置
        ├── configmap-patch.yaml   # 生产环境配置补丁
        ├── deployment-patch.yaml  # 生产环境部署补丁
        └── kustomization.yaml     # 生产环境 Kustomization
```

## 部署步骤

### 1. 提交配置到 Git 仓库

确保所有配置文件已提交到 Git 仓库的正确分支。

### 2. 应用 ArgoCD Application

```bash
kubectl apply -f argocd/apps/nezha-prod-app.yaml
```

### 3. 验证部署状态

在 ArgoCD UI 中查看 `nezha-prod` 应用的同步状态，或使用命令行：

```bash
# 检查 ArgoCD 应用状态
kubectl get application nezha-prod -n argocd

# 检查 Pod 状态
kubectl get pods -n nezha

# 检查服务状态
kubectl get svc -n nezha

# 检查 Ingress 状态
kubectl get ingress -n nezha
```

## 配置说明

### 核心组件

- **镜像**: `ghcr.io/nezhahq/nezha:v1.13.0`
- **端口**: 
  - HTTP: 8008 (面板访问)
  - gRPC: 5555 (Agent 连接)
- **存储**: 使用 emptyDir (无持久化)
- **数据库**: SQLite (存储在容器内)

### 访问配置

- **域名**: `nezha.liuovo.com`
- **协议**: HTTPS (自动重定向)
- **证书**: Let's Encrypt 自动颁发

### 资源配置

- **CPU**: 请求 200m，限制 1000m
- **内存**: 请求 256Mi，限制 1Gi
- **副本数**: 1

## 重要配置项

### OAuth 认证配置

生产环境建议配置 GitHub OAuth 认证。需要在 `configmap-patch.yaml` 中设置：

```yaml
oauth2:
  type: "github"
  admin: "your-github-username"
  clientid: "your-github-oauth-client-id"
  clientsecret: "your-github-oauth-client-secret"
```

### JWT 密钥

生产环境应该使用更安全的 JWT 密钥，建议使用 Kubernetes Secret 存储。

## 安全注意事项

1. **OAuth 配置**: 强烈建议配置 OAuth 认证而不是使用默认密码
2. **JWT 密钥**: 使用强密钥并定期轮换
3. **网络策略**: 考虑配置 NetworkPolicy 限制网络访问
4. **资源限制**: 已配置适当的资源限制防止资源滥用

## 监控和维护

### 健康检查

- **存活探针**: HTTP GET `/` 每30秒检查一次
- **就绪探针**: HTTP GET `/` 每10秒检查一次

### 日志查看

```bash
# 查看 Pod 日志
kubectl logs -f deployment/nezha-dashboard -n nezha

# 查看 ArgoCD 同步日志
kubectl logs -f deployment/argocd-application-controller -n argocd
```

### 故障排除

1. **Pod 无法启动**: 检查镜像拉取和配置文件
2. **无法访问**: 检查 Ingress 和 DNS 配置
3. **证书问题**: 检查 cert-manager 和 Let's Encrypt 配置

## 后续步骤

1. **配置 OAuth 认证**: 设置 GitHub OAuth 应用
2. **部署 Agent**: 在需要监控的服务器上部署哪吒 Agent
3. **配置告警**: 在面板中配置监控告警规则
4. **备份策略**: 考虑数据库备份方案（如果需要持久化）

## 相关链接

- [哪吒监控官方文档](https://nezha.wiki)
- [哪吒监控 GitHub](https://github.com/nezhahq/nezha)
- [ArgoCD 官方文档](https://argo-cd.readthedocs.io/)
