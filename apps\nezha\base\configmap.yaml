# 哪吒监控配置文件
apiVersion: v1
kind: ConfigMap
metadata:
  name: nezha-config
  labels:
    app: nezha
    app.kubernetes.io/name: nezha
    app.kubernetes.io/component: monitoring
data:
  config.yaml: |
    # 哪吒监控面板配置
    debug: false
    httpport: 8008
    grpcport: 5555
    
    # 数据库配置 (使用 SQLite)
    database:
      type: sqlite
      dsn: /data/nezha.db
    
    # JWT 密钥 (生产环境中应该使用 Secret)
    jwtkey: "nezha-jwt-secret-key-change-in-production"
    
    # 站点配置
    site:
      brand: "哪吒监控"
      cookiename: "nezha-dashboard"
      theme: "default"
      customcode: ""
      viewpassword: ""
    
    # OAuth 配置 (需要在生产环境中配置)
    oauth2:
      type: ""
      admin: ""
      clientid: ""
      clientsecret: ""
      endpoint: ""
    
    # 覆盖配置
    cover: 0
    
    # 忽略 IP 变化
    ignoreipdiff: false
    
    # IP 变化通知
    ipchangenotify: false
    
    # 实时传输
    realtimetransfer: true
    
    # 最大 TCP Ping 值
    maxtcppingvalue: 1000
