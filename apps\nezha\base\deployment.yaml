# 哪吒监控面板部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nezha-dashboard
  labels:
    app: nezha
    app.kubernetes.io/name: nezha
    app.kubernetes.io/component: dashboard
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nezha
      app.kubernetes.io/name: nezha
      app.kubernetes.io/component: dashboard
  template:
    metadata:
      labels:
        app: nezha
        app.kubernetes.io/name: nezha
        app.kubernetes.io/component: dashboard
    spec:
      containers:
        - name: nezha-dashboard
          image: ghcr.io/nezhahq/nezha:latest
          ports:
            - name: http
              containerPort: 8008
              protocol: TCP
            - name: grpc
              containerPort: 5555
              protocol: TCP
          env:
            - name: NEZHA_CONFIG_PATH
              value: "/etc/nezha/config.yaml"
          volumeMounts:
            - name: config
              mountPath: /etc/nezha
              readOnly: true
            - name: data
              mountPath: /data
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: config
          configMap:
            name: nezha-config
        - name: data
          emptyDir: {}
      restartPolicy: Always
