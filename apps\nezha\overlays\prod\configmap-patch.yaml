# 生产环境配置补丁
apiVersion: v1
kind: ConfigMap
metadata:
  name: nezha-config
data:
  config.yaml: |
    # 哪吒监控面板生产环境配置
    debug: false
    httpport: 8008
    grpcport: 5555
    
    # 数据库配置 (使用 SQLite)
    database:
      type: sqlite
      dsn: /data/nezha.db
    
    # JWT 密钥 (生产环境中应该使用更安全的密钥)
    jwtkey: "nezha-production-jwt-secret-key-2024"
    
    # 站点配置
    site:
      brand: "哪吒监控 - 生产环境"
      cookiename: "nezha-dashboard-prod"
      theme: "default"
      customcode: ""
      viewpassword: ""
    
    # OAuth 配置 (需要根据实际情况配置)
    # 建议使用 GitHub OAuth 进行身份验证
    oauth2:
      type: "github"
      admin: ""  # 管理员 GitHub 用户名，需要在部署时配置
      clientid: ""  # GitHub OAuth App Client ID，需要在部署时配置
      clientsecret: ""  # GitHub OAuth App Client Secret，需要在部署时配置
      endpoint: ""
    
    # 覆盖配置
    cover: 0
    
    # 忽略 IP 变化
    ignoreipdiff: false
    
    # IP 变化通知
    ipchangenotify: true
    
    # 实时传输
    realtimetransfer: true
    
    # 最大 TCP Ping 值
    maxtcppingvalue: 1000
