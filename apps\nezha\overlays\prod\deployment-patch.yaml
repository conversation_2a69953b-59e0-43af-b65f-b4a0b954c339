# 生产环境 Deployment 补丁
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nezha-dashboard
spec:
  template:
    spec:
      containers:
        - name: nezha-dashboard
          # 使用稳定版本而不是 latest
          image: ghcr.io/nezhahq/nezha:v1.13.0
          env:
            - name: NEZHA_CONFIG_PATH
              value: "/etc/nezha/config.yaml"
            # 生产环境特定环境变量
            - name: TZ
              value: "Asia/Shanghai"
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
          # 增强的健康检查
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
