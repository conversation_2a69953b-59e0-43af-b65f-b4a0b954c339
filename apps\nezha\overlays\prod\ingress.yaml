# 哪吒监控面板生产环境 Ingress 配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nezha-dashboard-ingress
  annotations:
    # 使用 Let's Encrypt 生产环境证书颁发者
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    # Traefik 特定注解
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    # 强制 HTTPS 重定向
    traefik.ingress.kubernetes.io/redirect-scheme: https
    traefik.ingress.kubernetes.io/redirect-permanent: "true"
  labels:
    app: nezha
    app.kubernetes.io/name: nezha
    app.kubernetes.io/component: dashboard
spec:
  ingressClassName: traefik
  rules:
    - host: "nezha.liuovo.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nezha-dashboard
                port:
                  number: 8008
  tls:
    - hosts:
        - "nezha.liuovo.com"
      secretName: nezha-liuovo-com-tls
