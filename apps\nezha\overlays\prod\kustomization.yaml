# 哪吒监控生产环境 Kustomization 配置
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# 引用基础配置
resources:
  - ../../base
  - ingress.yaml

# 命名空间
namespace: nezha

# 应用补丁
patches:
  - path: configmap-patch.yaml
    target:
      kind: ConfigMap
      name: nezha-config
  - path: deployment-patch.yaml
    target:
      kind: Deployment
      name: nezha-dashboard

# 生产环境特定标签
commonLabels:
  environment: production
  app.kubernetes.io/environment: production

# 镜像标签覆盖
images:
  - name: ghcr.io/nezhahq/nezha
    newTag: v1.13.0
